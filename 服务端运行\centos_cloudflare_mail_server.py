#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cloudflare邮件验证码获取 - Web API服务版本
CentOS服务端专用，供其他用户通过HTTP API调用
"""

import requests
import re
import quopri
import urllib3
import time
import ssl
import json
import logging
import os
import threading
from datetime import datetime
from flask import Flask, request, jsonify
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Optional, Dict, Any

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CloudflareMailClient:
    """Cloudflare邮件客户端 - Web API版本"""
    
    # 硬编码配置
    CONFIG = {
        "worker_domain": "api.91gmail.cn",
        "admin_password": "yu6709",
        "max_attempts": 40,
        "max_wait_time": 60,
        "request_timeout": 10,
        "retry_delay": 1,
        "log_level": "INFO",
        "log_file": os.path.join(os.path.dirname(os.path.abspath(__file__)), "centos_cloudflare_mail.log")
    }
    
    def __init__(self):
        """初始化客户端"""
        self.config = self.CONFIG.copy()
        self.logger = self._setup_logger()
        self.session = self._create_session()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('centos_cloudflare_mail')
        logger.setLevel(getattr(logging, self.config['log_level']))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(thread)d] - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        try:
            file_handler = logging.FileHandler(self.config['log_file'], encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            logger.warning(f"无法创建日志文件: {e}")
        
        return logger
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST", "DELETE"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        
        return session
    
    def get_verification_code(self, hidden_email: str, client_ip: str = "unknown") -> Optional[str]:
        """
        获取指定隐藏邮箱的验证码
        
        Args:
            hidden_email: 隐藏邮箱地址
            client_ip: 客户端IP地址（用于日志记录）
            
        Returns:
            验证码字符串，如果未找到则返回None
        """
        self.logger.info(f"[{client_ip}] 开始获取邮箱 {hidden_email} 的验证码...")
        
        max_attempts = self.config['max_attempts']
        max_time = self.config['max_wait_time']
        start_time = time.time()
        
        for attempt in range(max_attempts):
            # 检查是否超时
            elapsed_time = time.time() - start_time
            if elapsed_time > max_time:
                self.logger.warning(f"[{client_ip}] 获取验证码超时（{max_time}秒）")
                return None
            
            self.logger.debug(f"[{client_ip}] 第 {attempt + 1} 次尝试获取验证码...")
            
            try:
                # 获取邮件列表
                response = self._fetch_mails()
                if not response:
                    time.sleep(self.config['retry_delay'])
                    continue
                
                # 查找匹配的邮件
                verification_code = self._process_mails(response, hidden_email, client_ip)
                if verification_code:
                    self.logger.info(f"[{client_ip}] 成功获取验证码: {verification_code}")
                    return verification_code
                
                self.logger.debug(f"[{client_ip}] 未找到匹配的邮件，等待后重试...")
                time.sleep(self.config['retry_delay'])
                
            except Exception as e:
                self.logger.error(f"[{client_ip}] 获取验证码出错: {e}")
                time.sleep(self.config['retry_delay'])
        
        self.logger.warning(f"[{client_ip}] 尝试 {max_attempts} 次后未能获取验证码")
        return None
    
    def _fetch_mails(self) -> Optional[Dict[str, Any]]:
        """获取邮件列表"""
        headers = {
            'x-admin-auth': self.config['admin_password'],
            'Content-Type': 'application/json',
            'User-Agent': 'CloudflareMail-API/1.0 (CentOS Server)',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive'
        }
        
        try:
            response = self.session.get(
                f"https://{self.config['worker_domain']}/admin/mails",
                params={"limit": 50, "offset": 0},
                headers=headers,
                timeout=self.config['request_timeout'],
                verify=False,
                stream=True
            )
            
            if response.status_code != 200:
                self.logger.warning(f"API请求失败: {response.status_code}")
                return None
            
            data = response.json()
            if not data.get('results'):
                self.logger.debug("未找到任何邮件")
                return None
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取邮件列表失败: {e}")
            return None
    
    def _process_mails(self, data: Dict[str, Any], hidden_email: str, client_ip: str) -> Optional[str]:
        """处理邮件列表，查找验证码"""
        for mail in data['results']:
            raw_content = mail.get('raw', '')
            
            # 查找隐藏邮箱地址
            hide_email_match = re.search(
                r'Hide My Email[^<]*<([^>]+@icloud\.com(?:\.cn)?)>', 
                raw_content
            )
            if not hide_email_match:
                continue
            
            found_hidden_email = hide_email_match.group(1)
            if found_hidden_email.lower() != hidden_email.lower():
                continue
            
            # 找到匹配邮件，提取验证码
            verification_code = self._extract_code_from_mail(raw_content)
            
            # 删除已读邮件
            self._delete_mail(mail.get('id'), client_ip)
            
            if verification_code:
                return verification_code
            else:
                self.logger.debug(f"[{client_ip}] 邮件中未找到验证码")
        
        return None
    
    def _delete_mail(self, mail_id: str, client_ip: str) -> None:
        """删除邮件"""
        if not mail_id:
            return
        
        try:
            headers = {
                'x-admin-auth': self.config['admin_password'],
                'Content-Type': 'application/json'
            }
            
            self.session.delete(
                f"https://{self.config['worker_domain']}/admin/mails/{mail_id}",
                headers=headers,
                timeout=5,
                verify=False
            )
            self.logger.debug(f"[{client_ip}] 已删除邮件: {mail_id}")
        except Exception as e:
            self.logger.warning(f"[{client_ip}] 删除邮件失败: {e}")
    
    def _extract_code_from_mail(self, raw_content: str) -> Optional[str]:
        """从邮件原始内容中提取验证码"""
        # 解码QUOTED-PRINTABLE内容
        decoded_content = self._decode_mail_content(raw_content)
        if not decoded_content:
            return None
        
        # 提取纯文本
        clean_text = self._extract_clean_text(decoded_content)
        
        # 查找验证码
        return self._find_verification_code(clean_text)
    
    def _decode_mail_content(self, raw_content: str) -> str:
        """解码邮件内容"""
        qp_pattern = r'Content-Transfer-Encoding:\s*quoted-printable.*?\r?\n(?:.*?\r?\n)*?\r?\n(.*?)(?=\r?\n--|\Z)'
        qp_matches = re.findall(qp_pattern, raw_content, re.IGNORECASE | re.DOTALL)
        
        decoded_content = ""
        for qp_content in qp_matches:
            try:
                clean_content = qp_content.strip()
                if len(clean_content) > 20:
                    decoded_part = quopri.decodestring(clean_content.encode()).decode('utf-8', errors='ignore')
                    decoded_part = decoded_part.replace('=\r\n', '').replace('=\n', '')
                    decoded_content += decoded_part + "\n"
            except Exception:
                continue
        
        return decoded_content
    
    def _extract_clean_text(self, content: str) -> str:
        """提取纯文本，过滤HTML和CSS"""
        lines = content.split('\n')
        clean_lines = []
        in_html_section = False
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测HTML开始
            if line.startswith('<!doctype') or line.startswith('<html'):
                in_html_section = True
                continue
            
            if in_html_section:
                continue
            
            # 跳过HTML/CSS内容
            if any(keyword in line.lower() for keyword in [
                '<', '>', 'style', 'css', 'html', 'DOCTYPE', 'xmlns',
                'border:', 'margin:', 'padding:', 'background:', 'color:',
                'font-', 'text-', '@media', '.mj-', 'table', 'tbody'
            ]):
                continue
            
            # 跳过CSS样式行
            if '{' in line or '}' in line or line.endswith(';'):
                continue
            
            clean_lines.append(line)
        
        return '\n'.join(clean_lines)
    
    def _find_verification_code(self, text: str) -> Optional[str]:
        """在文本中查找验证码"""
        # 优先查找空格分隔的6位数字
        spaced_pattern = re.search(r'(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)', text)
        if spaced_pattern:
            return ''.join(spaced_pattern.groups())
        
        # 查找常见验证码模式
        patterns = [
            r'(?:code|verification|one-time)\s*(?:is|:)?\s*[:\s]*(\d{6})',
            r'(\d{6})\s*(?:is your)?\s*(?:code|verification)',
            r'Your\s+(?:verification\s+)?code\s*[:\s]*(\d{6})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        # 查找独立的6位数字
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['<', '>', 'style', 'css', 'html']):
                continue
            simple_match = re.search(r'\b(\d{6})\b', line)
            if simple_match:
                return simple_match.group(1)
        
        return None


# Flask Web应用
app = Flask(__name__)

# 全局客户端实例
mail_client = CloudflareMailClient()

# API配置
API_CONFIG = {
    "host": "0.0.0.0",  # 监听所有网络接口
    "port": 8080,       # 默认端口
    "debug": False,     # 生产环境关闭调试
    "threaded": True    # 启用多线程
}

@app.route('/api/verification-code', methods=['POST'])
def get_verification_code():
    """
    获取验证码API接口
    
    请求格式:
    POST /api/verification-code
    Content-Type: application/json
    {
        "email": "<EMAIL>"
    }
    
    响应格式:
    {
        "success": true,
        "email": "<EMAIL>",
        "verification_code": "123456",
        "message": "验证码获取成功",
        "timestamp": "2024-01-01T00:00:00"
    }
    """
    try:
        # 获取客户端IP
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', 
                                      request.environ.get('HTTP_X_REAL_IP', 
                                      request.remote_addr))
        
        # 解析请求数据
        if not request.is_json:
            return jsonify({"success": False}), 400
        
        data = request.get_json()
        email = data.get('email')
        
        if not email:
            return jsonify({"success": False}), 400
        
        # 验证邮箱格式
        if not re.match(r'^[^@]+@[^@]+\.[^@]+$', email):
            return jsonify({"success": False}), 400
        
        mail_client.logger.info(f"[{client_ip}] API请求: 获取邮箱 {email} 的验证码")
        
        # 获取验证码
        verification_code = mail_client.get_verification_code(email, client_ip)
        
        if verification_code:
            return jsonify({
                "success": True,
                "verification_code": verification_code
            })
        else:
            return jsonify({"success": False}), 404
            
    except Exception as e:
        mail_client.logger.error(f"[{client_ip}] API请求处理出错: {e}")
        return jsonify({"success": False}), 500



if __name__ == "__main__":
    print("=" * 50)
    print("Cloudflare Mail API 服务启动中...")
    print(f"监听地址: {API_CONFIG['host']}:{API_CONFIG['port']}")
    print("API接口:")
    print("  获取验证码: POST /api/verification-code")
    print("=" * 50)
    
    app.run(
        host=API_CONFIG['host'],
        port=API_CONFIG['port'],
        debug=API_CONFIG['debug'],
        threaded=API_CONFIG['threaded']
    )
