import requests
import re
import quopri
import urllib3
import time
import ssl
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置信息
WORKER_DOMAIN = "api.91gmail.cn"
ADMIN_PASSWORD = "yu6709"

# 创建自定义的SSL上下文
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

def get_verification_code(hidden_email):
    """
    获取指定隐藏邮箱的验证码，在30秒内每秒尝试一次
    
    参数:
        hidden_email (str): 隐藏邮箱地址，如 "<EMAIL>"
    
    返回:
        str: 验证码，如果找到的话；否则返回 None
    """
    print(f"开始获取邮箱 {hidden_email} 的验证码...")
    
    # 设置最大尝试次数和超时时间
    max_attempts = 40  # 最多30次尝试
    max_time = 60  # 最长等待60秒
    start_time = time.time()
    
    for attempt in range(max_attempts):
        # 检查是否超时
        elapsed_time = time.time() - start_time
        if elapsed_time > max_time:
            print(f"获取验证码超时（{max_time}秒）")
            return None
            
        print(f"第 {attempt + 1} 次尝试获取验证码...")
        
        try:
            # 创建带有重试策略的会话
            session = requests.Session()
            
            # 配置重试策略
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["GET", "POST", "DELETE"]
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("https://", adapter)
            
            # 配置请求头
            headers = {
                'x-admin-auth': ADMIN_PASSWORD,
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                'Connection': 'keep-alive'
            }
            
            # 获取邮件列表
            response = session.get(
                f"https://{WORKER_DOMAIN}/admin/mails",
                params={"limit": 50, "offset": 0},
                headers=headers,
                timeout=10,  # 增加超时时间
                verify=False,
                stream=True  # 使用流式传输
            )
            
            if response.status_code != 200:
                print(f"API请求失败: {response.status_code}")
                time.sleep(1)  # 固定等待1秒
                continue
                
            data = response.json()
            if not data.get('results'):
                print("未找到任何邮件，等待1秒后重试...")
                time.sleep(1)
                continue
                
            # 查找匹配的邮件
            for mail in data['results']:
                raw_content = mail.get('raw', '')
                
                # 查找隐藏邮箱地址
                hide_email_match = re.search(r'Hide My Email[^<]*<([^>]+@icloud\.com(?:\.cn)?)>', raw_content)
                if not hide_email_match:
                    continue
                    
                found_hidden_email = hide_email_match.group(1)
                if found_hidden_email.lower() != hidden_email.lower():
                    continue
                    
                # 找到匹配邮件，提取验证码
                verification_code = _extract_code_from_mail(raw_content)
                
                # 无论是否找到验证码，都删除该邮件
                mail_id = mail.get('id')
                if mail_id:
                    # 删除已读邮件
                    try:
                        session.delete(
                            f"https://{WORKER_DOMAIN}/admin/mails/{mail_id}",
                            headers=headers,
                            timeout=5,
                            verify=False
                        )
                    except Exception as e:
                        print(f"删除邮件失败: {e}")
                
                if verification_code:
                    print(f"成功获取验证码: {verification_code}")
                    return verification_code
                else:
                    print("邮件中未找到验证码，继续尝试...")
                    
            print("未找到匹配的邮件，等待1秒后重试...")
            time.sleep(1)
                
        except Exception as e:
            print(f"获取验证码出错: {e}")
            time.sleep(1)  # 出错后等待1秒再重试
    
    print(f"尝试 {max_attempts} 次后未能获取验证码")
    return None


def _extract_code_from_mail(raw_content):
    """从邮件原始内容中提取验证码"""
    # 解码QUOTED-PRINTABLE内容
    decoded_content = _decode_mail_content(raw_content)
    if not decoded_content:
        return None
        
    # 提取纯文本
    clean_text = _extract_clean_text(decoded_content)
    
    # 查找验证码
    return _find_verification_code(clean_text)


def _decode_mail_content(raw_content):
    """解码邮件内容"""
    qp_pattern = r'Content-Transfer-Encoding:\s*quoted-printable.*?\r?\n(?:.*?\r?\n)*?\r?\n(.*?)(?=\r?\n--|\Z)'
    qp_matches = re.findall(qp_pattern, raw_content, re.IGNORECASE | re.DOTALL)
    
    decoded_content = ""
    for qp_content in qp_matches:
        try:
            clean_content = qp_content.strip()
            if len(clean_content) > 20:
                decoded_part = quopri.decodestring(clean_content.encode()).decode('utf-8', errors='ignore')
                decoded_part = decoded_part.replace('=\r\n', '').replace('=\n', '')
                decoded_content += decoded_part + "\n"
        except:
            continue
            
    return decoded_content


def _extract_clean_text(content):
    """提取纯文本，过滤HTML和CSS"""
    lines = content.split('\n')
    clean_lines = []
    in_html_section = False
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 检测HTML开始
        if line.startswith('<!doctype') or line.startswith('<html'):
            in_html_section = True
            continue
            
        if in_html_section:
            continue
            
        # 跳过HTML/CSS内容
        if any(keyword in line.lower() for keyword in [
            '<', '>', 'style', 'css', 'html', 'DOCTYPE', 'xmlns',
            'border:', 'margin:', 'padding:', 'background:', 'color:',
            'font-', 'text-', '@media', '.mj-', 'table', 'tbody'
        ]):
            continue
            
        # 跳过CSS样式行
        if '{' in line or '}' in line or line.endswith(';'):
            continue
            
        clean_lines.append(line)
    
    return '\n'.join(clean_lines)


def _find_verification_code(text):
    """在文本中查找验证码"""
    # 优先查找空格分隔的6位数字
    spaced_pattern = re.search(r'(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)', text)
    if spaced_pattern:
        return ''.join(spaced_pattern.groups())
    
    # 查找常见验证码模式
    patterns = [
        r'(?:code|verification|one-time)\s*(?:is|:)?\s*[:\s]*(\d{6})',
        r'(\d{6})\s*(?:is your)?\s*(?:code|verification)',
        r'Your\s+(?:verification\s+)?code\s*[:\s]*(\d{6})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1)
    
    # 查找独立的6位数字
    lines = text.split('\n')
    for line in lines:
        line = line.strip()
        if any(keyword in line.lower() for keyword in ['<', '>', 'style', 'css', 'html']):
            continue
        simple_match = re.search(r'\b(\d{6})\b', line)
        if simple_match:
            return simple_match.group(1)
    
    return None


# 测试示例
if __name__ == "__main__":
    hidden_email = input("请输入要测试的邮箱地址: ")
    if not hidden_email:
        hidden_email = "<EMAIL>"  # 默认测试邮箱
        
    code = get_verification_code(hidden_email)
    
    if code:
        print(f"最终验证码: {code}")
    else:
        print("未能获取验证码")