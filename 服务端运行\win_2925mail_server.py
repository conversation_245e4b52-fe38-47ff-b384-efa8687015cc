#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Windows服务器专用邮件验证码服务端
- 1秒极速检查间隔
- 多线程并行处理（3个线程）
- 自动删除5分钟以上的邮件
- 读取后立即删除邮件，避免重复处理
- 30天会话保持，重启无需重新登录

=== Windows服务器部署说明 ===

1. 安装依赖库：
   pip install flask requests

2. 在Windows服务器上运行：
   # 前台运行（测试用）
   python 2925mail_server_win.py

   # 后台运行（生产环境）
   start /B python 2925mail_server_win.py

3. 停止服务：
   taskkill /F /IM python.exe

4. 查看运行状态：
   tasklist | findstr python

服务器地址为 *************:8080
"""

import requests
import json
import time
import threading
import re
import hashlib
import os
import random
import string
import signal
import sys
from datetime import datetime
from flask import Flask, jsonify, request
import logging
from logging.handlers import RotatingFileHandler


# 配置日志轮转 (2个文件，每个最大10MB)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Windows服务器日志文件配置
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, 'mail_server.log')

# 创建轮转文件处理器
file_handler = RotatingFileHandler(
    log_file,
    maxBytes=10*1024*1024,  # 10MB
    backupCount=2,          # 保留2个备份文件
    encoding='utf-8'
)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# 控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# 添加处理器
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 邮箱配置
USERNAME = "<EMAIL>"
PASSWORD = "yuyu6709"

# 验证码缓存系统
verification_code_cache = {}  # 格式: {收件人: {"code": 验证码, "timestamp": 时间戳, "mail_id": 邮件ID}}
CACHE_EXPIRE_MINUTES = 5  # 缓存过期时间（分钟）

# 网络请求超时设置（秒）
REQUEST_TIMEOUT = 10  # 一般请求超时
LOGIN_TIMEOUT = 30    # 登录请求超时（可能较慢）

# 邮件检测配置





# 查询锁（防止并发重复查询）
import threading
query_lock = threading.Lock()

# 登录状态（内存变量保存，简化代码）
current_session = None
login_time = None

# 预编译正则表达式模式（性能优化）
VERIFICATION_CODE_PATTERNS = [
    re.compile(r'(?:verification code|验证码)(?:\s*is)?(?:\s*[:：])\s*(\d{4,8})', re.IGNORECASE),
    re.compile(r'验证码[:：]\s*(\d{4,8})', re.IGNORECASE),
    re.compile(r'(?:code|代码)[:：]\s*(\d{4,8})', re.IGNORECASE),
    re.compile(r'(?:otp|动态密码)[:：]\s*(\d{4,8})', re.IGNORECASE),
    re.compile(r'\b(\d{6})\b', re.IGNORECASE),
    re.compile(r'(?<!\d)(\d{4,8})(?!\d)', re.IGNORECASE),
]

# HTML标签清理的预编译正则
HTML_TAG_PATTERN = re.compile(r'<[^>]+>')
WHITESPACE_PATTERN = re.compile(r'\s+')

# Flask应用
app = Flask(__name__)

def clean_expired_cache():
    """清理过期的缓存数据（优化版：减少时间计算）"""
    current_timestamp = time.time()
    expire_threshold = CACHE_EXPIRE_MINUTES * 60  # 转换为秒
    expired_keys = []

    # 直接比较时间戳，避免datetime对象创建和计算
    for recipient, data in verification_code_cache.items():
        if current_timestamp - data['timestamp'] > expire_threshold:
            expired_keys.append(recipient)

    for key in expired_keys:
        del verification_code_cache[key]

    if expired_keys:
        logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")





def add_to_cache(recipient, verification_code, mail_id):
    """添加验证码到缓存（邮箱地址转为小写）"""
    recipient_lower = recipient.lower()  # 转为小写
    verification_code_cache[recipient_lower] = {
        'code': verification_code,
        'timestamp': time.time(),
        'mail_id': mail_id
    }
    logger.info(f"缓存验证码: {recipient_lower} -> {verification_code}")

def get_from_cache(recipient):
    """从缓存获取验证码（一次性使用：获取后立即删除）"""
    recipient_lower = recipient.lower()  # 转为小写
    if recipient_lower in verification_code_cache:
        data = verification_code_cache[recipient_lower]
        current_timestamp = time.time()
        expire_threshold = CACHE_EXPIRE_MINUTES * 60  # 转换为秒

        # 直接比较时间戳，避免datetime对象创建
        if current_timestamp - data['timestamp'] <= expire_threshold:
            code = data['code']
            # 获取后立即删除（一次性使用）
            del verification_code_cache[recipient_lower]
            logger.info(f"验证码已使用并删除: {recipient_lower}")
            return code
        else:
            # 过期了，删除
            del verification_code_cache[recipient_lower]
            logger.info(f"验证码已过期并删除: {recipient_lower}")

    return None

def extract_verification_code(mail_content):
    """从邮件内容中提取验证码"""
    if not mail_content:
        return None
    
    try:
        # 获取邮件正文内容
        body_text = mail_content.get('bodyText', '')
        body_html = mail_content.get('bodyHtmlText', '')
        
        # 优先使用纯文本内容，如果没有则使用HTML内容
        content = body_text if body_text else body_html
        
        if not content:
            return None
        
        # 如果是HTML内容，先尝试去除HTML标签（使用预编译正则）
        if '<html' in content.lower() or '<body' in content.lower():
            clean_content = HTML_TAG_PATTERN.sub(' ', content)
            clean_content = WHITESPACE_PATTERN.sub(' ', clean_content).strip()
        else:
            clean_content = content

        # 使用预编译的正则表达式模式匹配
        found_codes = []
        for pattern in VERIFICATION_CODE_PATTERNS:
            matches = pattern.findall(clean_content)
            if matches:
                found_codes.extend(matches)
        
        if not found_codes:
            return None
        
        # 去重并过滤
        unique_codes = list(set(found_codes))
        
        # 优先选择6位数字的验证码
        six_digit_codes = [code for code in unique_codes if len(code) == 6 and code.isdigit()]
        if six_digit_codes:
            return six_digit_codes[0]
        
        # 如果没有6位数字，选择4-8位的数字
        valid_codes = [code for code in unique_codes if 4 <= len(code) <= 8 and code.isdigit()]
        if valid_codes:
            valid_codes.sort(key=lambda x: (abs(len(x) - 6), x))
            return valid_codes[0]
        
        return None
        
    except Exception as e:
        logger.error(f"提取验证码时发生异常: {e}")
        return None

def get_token(headers, cookies):
    """获取认证token"""
    try:
        session = requests.Session()
        session.headers.update(headers)
        session.cookies.update(cookies)

        url = "https://2925.com/mailv2/auth/token"
        data = {"timeout": 5000}

        response = session.post(url, json=data, timeout=REQUEST_TIMEOUT)

        if response.status_code != 200:
            logger.error(f"获取token失败，状态码: {response.status_code}")
            return None

        try:
            result = response.json()
            if 'result' in result and result['result'] and 'token' in result['result']:
                token = result['result']['token']
                logger.info(f"成功获取token")
                return token
        except:
            logger.error("token响应解析失败")

        return None

    except Exception as e:
        logger.error(f"获取token时发生异常: {e}")
        return None



def login():
    """登录邮箱系统"""
    global login_session, login_time

    try:
        logger.info(f"正在登录: {USERNAME}")

        session = requests.Session()
        headers = {
            "user-agent": "Vincent/5.0 (X11; Linux x86_64) ",
        }
        session.headers.update(headers)

        # 登录API地址
        url = "https://2925.com/mailv2/auth/weblogin"

        # 准备登录数据
        data = {
            "uname": USERNAME,
            "rsapwd": hashlib.md5(PASSWORD.encode('utf-8')).hexdigest(),
            "pass": "",
            "rememberLogin": "true"
        }

        # 发送登录请求
        response = session.post(url, data=data, timeout=LOGIN_TIMEOUT)

        # 检查状态码
        if response.status_code != 200:
            logger.error(f"登录请求失败，状态码: {response.status_code}")
            return None

        # 解析响应
        try:
            result = response.json()
            logger.info(f"登录响应: {json.dumps(result, ensure_ascii=False)[:200]}...")
        except:
            logger.warning("响应不是有效的JSON格式")
            return None

        # 获取并保存Cookie
        cookies = session.cookies.get_dict()

        if not cookies or 'aut' not in cookies:
            logger.error(f"登录失败，未获取到有效cookies")
            return None

        # 保存jwt_token
        cookies['jwt_token'] = cookies['aut']

        # 尝试从登录响应中获取token
        token = None
        if 'result' in result and result['result'] and 'token' in result['result']:
            token = result['result']['token']
            logger.info(f"从登录响应中获取到token")

        # 如果登录响应中没有token，则调用token API获取
        if not token:
            logger.info("登录响应中没有token，尝试调用token API获取...")
            token = get_token(session.headers, cookies)
            if not token:
                logger.error(f"获取token失败")
                return None

        # 更新Authorization头
        headers['authorization'] = f"Bearer {token}"
        session.headers.update(headers)
        session.cookies.update(cookies)

        # 保存到内存变量
        global current_session
        current_session = session
        login_time = datetime.now()

        logger.info("登录成功，会话已保存到内存")
        return session

    except Exception as e:
        logger.error(f"登录时发生异常: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return None

def is_session_valid():
    """检查会话是否有效"""
    global current_session, login_time

    if not current_session or not login_time:
        return False

    # 不再检查时间过期，会话有效性通过实际API调用的401错误来判断
    return True

def get_session():
    """获取有效的会话（简化版：只使用内存变量）"""
    global current_session

    # 检查内存中的会话
    if is_session_valid():
        return current_session

    # 重新登录
    logger.info("内存会话无效，重新登录...")
    return login()

def get_single_mail_content(session, message_id):
    """获取单封邮件的完整内容"""
    if not session or not message_id:
        return None
        
    try:
        url = 'https://2925.com/mailv2/maildata/MailRead/mails/read'
        
        # 生成traceId
        trace_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
        
        # 使用正确的参数格式
        params = {
            'MessageID': message_id,
            'FolderName': 'Inbox',
            'MailBox': USERNAME,
            'IsPre': 'false',
            'traceId': trace_id
        }
        
        # 发送GET请求
        response = session.get(url, params=params, timeout=REQUEST_TIMEOUT)
        
        if response.status_code != 200:
            logger.error(f"读取邮件失败，状态码: {response.status_code}")
            if response.status_code == 401:
                logger.warning("读取邮件时检测到401错误，会话已失效")
                # 清除所有会话数据
                global current_session, login_time
                current_session = None
                login_time = None
            return None
            
        # 解析响应
        try:
            result = response.json()
        except Exception as e:
            logger.error(f"响应解析失败: {e}")
            return None
            
        if 'result' not in result:
            logger.warning(f"响应格式错误: {result}")
            return None
            
        return result['result']
        
    except Exception as e:
        logger.error(f"获取邮件内容时发生异常: {e}")
        return None

def delete_mail(session, message_id):
    """删除邮件（移动到垃圾箱）"""
    if not session or not message_id:
        return False

    try:
        # 生成traceId
        trace_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))

        url = f'https://2925.com/mailv2/maildata/MailData/mails/folder?traceId={trace_id}'

        # 移动邮件到垃圾箱的参数
        data = {
            'messageIds': [message_id],
            'fromFolder': 'Inbox',
            'toFolder': 'Trash',  # 移动到垃圾箱
            'mailBox': USERNAME
        }

        # 发送PUT请求
        response = session.put(url, json=data, timeout=REQUEST_TIMEOUT)

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('result') and result.get('code') == 200:
                    logger.info(f"邮件删除成功: {message_id[:8]}...")
                    return True
                else:
                    logger.error(f"邮件删除失败: {result.get('message', '未知错误')}")
                    return False
            except:
                logger.info(f"邮件删除成功: {message_id[:8]}...")
                return True
        else:
            logger.error(f"邮件删除失败，状态码: {response.status_code}")
            if response.status_code == 401:
                logger.warning("删除邮件时检测到401错误，会话已失效")
                # 清除所有会话数据
                global current_session, login_time
                current_session = None
                login_time = None
            try:
                error_response = response.json()
                logger.error(f"删除失败详情: {error_response}")
            except:
                logger.error(f"删除失败响应: {response.text}")
            return False

    except Exception as e:
        logger.error(f"删除邮件时发生异常: {e}")
        return False

def get_recent_mails(session):
    """获取最近的邮件（简化版：只获取第一页）"""
    try:
        url = 'https://2925.com/mailv2/maildata/MailList/mails'
        params = {
            'Folder': 'Inbox',
            'MailBox': USERNAME,
            'FilterType': '0',
            'PageIndex': '1',
            'PageCount': '50',  # 只获取最近50封邮件
            'traceId': f'trace_{int(time.time() * 1000)}'
        }

        response = session.get(url, params=params, timeout=REQUEST_TIMEOUT)

        if response.status_code != 200:
            logger.error(f"获取邮件列表失败，状态码: {response.status_code}")
            if response.status_code == 401:
                logger.warning("检测到401未授权错误，会话已失效")
                global current_session, login_time
                current_session = None
                login_time = None
            return []

        result = response.json()
        if 'result' not in result:
            logger.warning(f"邮件列表响应格式错误: {result}")
            return []

        mail_list = result['result'].get('list', [])
        return mail_list or []

    except Exception as e:
        logger.error(f"获取邮件列表异常: {e}")
        return []

def is_mail_too_old(mail):
    """检查邮件是否超过5分钟"""
    try:
        create_time = mail.get('createTime', '')
        if not create_time or not str(create_time).isdigit():
            return False

        timestamp = int(create_time) / 1000
        mail_time = datetime.fromtimestamp(timestamp)
        current_time = datetime.now()

        age_minutes = (current_time - mail_time).total_seconds() / 60
        return age_minutes > 5  # 直接硬编码5分钟

    except Exception:
        return False  # 简化错误处理



def check_new_mails(session):
    """检查新邮件并提取验证码（简化版）"""
    try:
        all_mails = get_recent_mails(session)
        if not all_mails:
            return

        new_mails_count = 0
        deleted_mails_count = 0

        for mail in all_mails:
            message_id = mail.get('messageId', '')
            to_addresses = mail.get('toAddress', [])

            if not message_id:
                continue

            # 检查邮件是否太旧，直接删除
            if is_mail_too_old(mail):
                if delete_mail(session, message_id):
                    deleted_mails_count += 1
                continue

            # 获取收件人
            recipient = None
            if isinstance(to_addresses, list) and to_addresses:
                recipient = to_addresses[0]
            elif isinstance(to_addresses, str):
                recipient = to_addresses

            if not recipient:
                continue

            # 检查是否已经在缓存中（只检查不删除）
            recipient_lower = recipient.lower()
            if recipient_lower in verification_code_cache:
                # 检查是否过期
                data = verification_code_cache[recipient_lower]
                current_timestamp = time.time()
                expire_threshold = CACHE_EXPIRE_MINUTES * 60
                if current_timestamp - data['timestamp'] <= expire_threshold:
                    continue  # 已有有效验证码，跳过

            # 读取邮件内容并提取验证码
            mail_content = get_single_mail_content(session, message_id)
            if mail_content:
                verification_code = extract_verification_code(mail_content)
                if verification_code:
                    add_to_cache(recipient, verification_code, message_id)
                    new_mails_count += 1
                    logger.info(f"✅ 新验证码: {recipient} -> {verification_code}")

                # 删除已读取的邮件
                if delete_mail(session, message_id):
                    deleted_mails_count += 1

        if new_mails_count > 0 or deleted_mails_count > 0:
            logger.info(f"本次检查: 新增 {new_mails_count} 个验证码, 删除 {deleted_mails_count} 封邮件")

        # 清理过期缓存
        clean_expired_cache()

    except Exception as e:
        logger.error(f"检查新邮件时发生异常: {e}")

def check_mail_for_recipient(recipient):
    """按需查询指定收件人的邮件（防重复查询优化）"""
    try:
        # 首先检查缓存中是否已有该收件人的验证码
        recipient_lower = recipient.lower()
        if recipient_lower in verification_code_cache:
            data = verification_code_cache[recipient_lower]
            current_timestamp = time.time()
            expire_threshold = CACHE_EXPIRE_MINUTES * 60
            if current_timestamp - data['timestamp'] <= expire_threshold:
                logger.info(f"缓存命中: {recipient} -> {data['code']}")
                return data['code']
            else:
                # 过期了，删除
                del verification_code_cache[recipient_lower]
                logger.info(f"缓存已过期，删除: {recipient}")

        # 缓存中没有，需要查询邮件列表
        # 使用锁防止并发重复查询
        with query_lock:
            # 再次检查缓存（可能在等待锁的过程中其他线程已经查询并缓存了）
            if recipient_lower in verification_code_cache:
                data = verification_code_cache[recipient_lower]
                current_timestamp = time.time()
                expire_threshold = CACHE_EXPIRE_MINUTES * 60
                if current_timestamp - data['timestamp'] <= expire_threshold:
                    logger.info(f"等待期间缓存命中: {recipient} -> {data['code']}")
                    return data['code']

            # 执行查询
            logger.info(f"缓存未命中，执行查询: {recipient}")
            session = get_session()
            if not session:
                logger.error("无法获取会话，查询失败")
                return None

            # 查询邮件并处理
            check_new_mails(session)

        # 查询完成后再次检查缓存
        if recipient_lower in verification_code_cache:
            data = verification_code_cache[recipient_lower]
            current_timestamp = time.time()
            expire_threshold = CACHE_EXPIRE_MINUTES * 60
            if current_timestamp - data['timestamp'] <= expire_threshold:
                logger.info(f"查询后找到验证码: {recipient} -> {data['code']}")
                return data['code']

        logger.info(f"查询完成，未找到验证码: {recipient}")
        return None

    except Exception as e:
        logger.error(f"按需查询邮件异常: {e}")
        return None

# Flask路由
@app.route('/', methods=['GET'])
def index():
    """根路径处理，提供服务基本信息"""
    return jsonify({
        'service': '2925 Mail Service',
        'status': 'running',
        'endpoints': ['/health', '/get_email', '/get_code', '/get_domains'],
        'documentation': '请参考API文档使用相应的端点'
    })

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'version': '1.0.0'
    })

@app.route('/get_email', methods=['GET'])
def get_email():
    """生成随机邮箱地址"""
    try:
        # 生成7位随机字符（大小写字母+数字）
        chars = string.ascii_letters + string.digits  # a-z, A-Z, 0-9
        random_suffix = ''.join(random.choices(chars, k=7))
        random_email = f"codegmail{random_suffix}@2925.com"

        logger.info(f"📧 API调用 - 生成邮箱: {random_email}")

        return jsonify({
            'success': True,
            'email': random_email,
            'message': '随机邮箱生成成功'
        })

    except Exception as e:
        logger.error(f"生成随机邮箱异常: {e}")
        return jsonify({
            'success': False,
            'email': None,
            'message': f'服务器错误: {str(e)}'
        }), 500

@app.route('/get_domains', methods=['GET'])
def get_domains():
    """获取可用域名"""
    return jsonify({
        'success': True,
        'message': '成功获取可用域名',
        'domains': ['2925.com']
    })

@app.route('/get_code', methods=['GET'])
def get_code():
    """获取验证码"""
    email = request.args.get('email')

    if not email:
        return jsonify({
            'success': False,
            'message': '缺少email参数',
            'code': None
        }), 400

    try:
        # 使用按需查询模式
        code = check_mail_for_recipient(email)

        if code:
            logger.info(f"✅ API调用 - 获取验证码: {email} -> {code} (按需查询)")
            return jsonify({
                'success': True,
                'recipient': email,  # 保持原始大小写
                'code': code,
                'message': '验证码获取成功（按需查询）'
            })
        else:
            logger.info(f"❌ API调用 - 未找到验证码: {email} (按需查询完成)")
            return jsonify({
                'success': False,
                'recipient': email,  # 保持原始大小写
                'code': None,
                'message': '未找到验证码，请稍后重试'
            }), 404

    except Exception as e:
        logger.error(f"获取验证码异常: {e}")
        return jsonify({
            'success': False,
            'recipient': email,
            'code': None,
            'message': f'服务器错误: {str(e)}'
        }), 500

@app.route('/api/verification_code/<recipient>', methods=['GET'])
def get_verification_code(recipient):
    """获取指定收件人的验证码（兼容性接口）"""
    try:
        # 使用按需查询模式
        code = check_mail_for_recipient(recipient)

        if code:
            logger.info(f"✅ API调用 - 获取验证码: {recipient} -> {code} (按需查询)")
            return jsonify({
                'success': True,
                'recipient': recipient,
                'code': code,
                'message': '验证码获取成功（按需查询）'
            })
        else:
            logger.info(f"❌ API调用 - 未找到验证码: {recipient} (按需查询完成)")
            return jsonify({
                'success': False,
                'recipient': recipient,
                'code': None,
                'message': '未找到验证码，请稍后重试'
            }), 404

    except Exception as e:
        logger.error(f"API异常: {e}")
        return jsonify({
            'success': False,
            'recipient': recipient,
            'code': None,
            'message': f'服务器错误: {str(e)}'
        }), 500

@app.route('/api/cache_status', methods=['GET'])
def get_cache_status():
    """获取缓存状态"""
    try:
        clean_expired_cache()

        cache_info = {}
        current_time = datetime.now()

        for recipient, data in verification_code_cache.items():
            cache_time = datetime.fromtimestamp(data['timestamp'])
            age_minutes = (current_time - cache_time).total_seconds() / 60
            remaining_minutes = CACHE_EXPIRE_MINUTES - age_minutes

            cache_info[recipient] = {
                'code': data['code'],
                'remaining_minutes': round(remaining_minutes, 1),
                'mail_id': data['mail_id']
            }

        return jsonify({
            'success': True,
            'cache_count': len(cache_info),
            'cache_data': cache_info,
            'message': f'当前缓存 {len(cache_info)} 个验证码'
        })

    except Exception as e:
        logger.error(f"获取缓存状态异常: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500



def signal_handler(sig, _):
    """Windows信号处理器"""
    logger.info(f"收到信号 {sig}，正在优雅关闭服务...")
    sys.exit(0)

if __name__ == "__main__":
    # 注册Windows信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 启动时尝试加载已保存的会话
        logger.info("启动Windows邮件验证码服务器...")
        logger.info(f"工作目录: {script_dir}")
        logger.info(f"日志文件: {log_file}")
        logger.info("会话管理: 内存变量保存（简化模式）")

        # 启动Flask服务
        logger.info("服务配置:")
        logger.info("  查询模式: 按需查询（客户端请求时才查询邮件）")
        logger.info("  最大邮件年龄: 5 分钟")
        logger.info(f"  缓存过期时间: {CACHE_EXPIRE_MINUTES} 分钟")
        logger.info("  会话管理: 智能检测失效（通过401错误）")
        logger.info(f"  服务端口: 8080")
        logger.info("API端点:")
        logger.info("  GET / - 服务基本信息")
        logger.info("  GET /health - 健康检查")
        logger.info("  GET /get_email - 获取随机邮箱账号")
        logger.info("  GET /get_code?email=<email> - 获取验证码")
        logger.info("  GET /get_domains - 获取可用域名")
        logger.info("  GET /api/verification_code/<recipient> - 获取验证码（兼容接口）")
        logger.info("  GET /api/cache_status - 获取缓存状态")
        logger.info("Windows服务器启动完成！")

        app.run(host='0.0.0.0', port=8080, debug=False, threaded=True)

    except KeyboardInterrupt:
        logger.info("收到Ctrl+C中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"Windows服务启动失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
    finally:
        logger.info("Windows邮件验证码服务已关闭")
