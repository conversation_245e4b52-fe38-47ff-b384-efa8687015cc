#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cloudflare邮件验证码获取 - 简化客户端（本地测试版）
"""

import requests
import json
import time
import sys

class CloudflareMailAPIClient:
    """Cloudflare邮件API客户端"""
    
    def __init__(self, api_url: str = "http://127.0.0.1:8080"):
        """初始化客户端"""
        self.api_url = api_url.rstrip('/')
        self.endpoint = f"{self.api_url}/api/verification-code"
        
    def get_verification_code(self, email: str, timeout: int = 30):
        try:
            # 准备请求数据
            data = {"email": email}
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "CloudflareMailClient/1.0"
            }
            
            print(f"正在获取邮箱 {email} 的验证码...")
            
            # 发送POST请求
            response = requests.post(
                self.endpoint,
                json=data,
                headers=headers,
                timeout=timeout
            )
            
            # 解析响应
            result = response.json()
            
            if response.status_code == 200 and result.get("success"):
                print(f"✅ 成功获取验证码: {result.get('verification_code')}")
                return result
            else:
                print(f"❌ 获取验证码失败")
                return {"success": False, "email": email}
                
        except requests.exceptions.Timeout:
            print(f"❌ 请求超时: {timeout}秒")
            return {"success": False, "email": email}
            
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败: 无法连接到 {self.api_url}")
            return {"success": False, "email": email}
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求错误: {e}")
            return {"success": False, "email": email}
            
        except json.JSONDecodeError:
            print("❌ 服务器响应格式错误")
            return {"success": False, "email": email}
            
        except Exception as e:
            print(f"❌ 未知错误: {e}")
            return {"success": False, "email": email}
    



def main():
    """硬编码测试邮箱的主函数"""
    # 硬编码测试邮箱地址
    TEST_EMAIL = "<EMAIL>"  # 修改这里的邮箱地址进行测试
    
    print("=" * 50)
    print("Cloudflare邮件验证码获取客户端 (本地测试版)")
    print("=" * 50)
    
    try:
        print(f"\n目标邮箱: {TEST_EMAIL}")
        print("正在连接到API服务器...")
        
        # 创建客户端并获取验证码
        client = CloudflareMailAPIClient()
        result = client.get_verification_code(TEST_EMAIL)
        
        # 输出结果
        print("\n" + "=" * 50)
        if result.get("success"):
            print(f"🎉 验证码获取成功!")
            print(f"📧 邮箱: {TEST_EMAIL}")
            print(f"🔢 验证码: {result.get('verification_code')}")
        else:
            print(f"💥 验证码获取失败!")
            print(f"📧 邮箱: {TEST_EMAIL}")
        print("=" * 50)
        
        # 设置退出码
        sys.exit(0 if result.get("success") else 1)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  操作被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
